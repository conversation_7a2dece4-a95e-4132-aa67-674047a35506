import { TelegramClient, Api } from "telegram";
import { StringSession } from "telegram/sessions/index.js";
import readline from "readline";
import fs from "fs";
import path from "path";

const INPUT_FILE = "./star-gifts.json"; // Use raw file with fileReference data
const IMAGES_DIR = "./images";
const TEMP_DIR = "./temp";

// Telegram API credentials
const apiId = 14431416;
const apiHash = "da8fa0a17dd9e0c1b9e420d73a39a710";

// Ensure directories exist
[IMAGES_DIR, TEMP_DIR].forEach((dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

async function downloadStickerFile(client, sticker, collectionId) {
  try {
    // Convert fileReference buffer data back to Buffer
    const fileReference = Buffer.from(sticker.fileReference.data);

    // Create input location for the sticker using BigInt for id and accessHash
    const inputLocation = new Api.InputDocumentFileLocation({
      id: BigInt(sticker.id),
      accessHash: BigInt(sticker.accessHash),
      fileReference: fileReference,
      thumbSize: "", // Empty for full file
    });

    console.log(`📥 Downloading sticker for collection ${collectionId}...`);

    // Download the file - try without fileSize first, then with BigInt if needed
    const result = await client.downloadFile(inputLocation, {
      dcId: sticker.dcId,
    });

    // Save the downloaded file
    const tempFilename = `${collectionId}.tgs`;
    const tempFilepath = path.join(TEMP_DIR, tempFilename);

    fs.writeFileSync(tempFilepath, result);

    console.log(`✅ Downloaded sticker for collection ${collectionId}`);
    return tempFilepath;
  } catch (error) {
    console.error(
      `❌ Error downloading sticker for collection ${collectionId}:`,
      error.message
    );
    return null;
  }
}

async function createDownloadMarker(collectionId, emoji, stars) {
  try {
    // Create a simple marker file to indicate successful download
    const markerPath = path.join(IMAGES_DIR, `${collectionId}.downloaded`);
    const markerData = {
      collectionId,
      emoji,
      stars,
      downloadedAt: new Date().toISOString(),
      tgsFile: `${collectionId}.tgs`,
    };

    fs.writeFileSync(markerPath, JSON.stringify(markerData, null, 2));
    console.log(`✅ Created download marker for collection ${collectionId}`);
    return markerPath;
  } catch (error) {
    console.error(
      `❌ Error creating marker for collection ${collectionId}:`,
      error.message
    );
    return null;
  }
}

async function processCollections() {
  const stringSession = new StringSession("");

  const client = new TelegramClient(stringSession, apiId, apiHash, {
    connectionRetries: 5,
  });

  try {
    // Authenticate
    await client.start({
      phoneNumber: () =>
        new Promise((resolve) => rl.question("Enter phone number: ", resolve)),
      phoneCode: () =>
        new Promise((resolve) => rl.question("Enter code: ", resolve)),
      password: () =>
        new Promise((resolve) =>
          rl.question("Enter password (if 2FA): ", resolve)
        ),
      onError: (err) => console.log("Error:", err),
    });

    console.log("🔐 Connected to Telegram");

    // Read gifts data
    console.log("📖 Reading gifts data...");
    const rawData = fs.readFileSync(INPUT_FILE, "utf8");
    const data = JSON.parse(rawData);

    console.log(`🎁 Processing ${data.gifts.length} gifts...`);

    let processedCount = 0;
    let skippedCount = 0;

    for (let i = 0; i < data.gifts.length; i++) {
      const gift = data.gifts[i];

      try {
        const collectionId = gift.id.toString();
        const stars = gift.stars.toString();

        // Extract emoji from sticker attributes
        let emoji = "🎁"; // Default emoji
        if (gift.sticker?.attributes) {
          const customEmojiAttr = gift.sticker.attributes.find(
            (attr) => attr.className === "DocumentAttributeCustomEmoji"
          );
          if (customEmojiAttr?.alt) {
            emoji = customEmojiAttr.alt;
          }
        }

        // Check if already downloaded
        const markerFilename = `${collectionId}.downloaded`;
        const markerPath = path.join(IMAGES_DIR, markerFilename);

        if (fs.existsSync(markerPath)) {
          console.log(
            `⏭️  Skipping collection ${collectionId} - already downloaded`
          );
          processedCount++;
          continue;
        }

        // Download sticker file
        const tgsFilePath = await downloadStickerFile(
          client,
          gift.sticker,
          collectionId
        );

        if (tgsFilePath) {
          // Create download marker
          await createDownloadMarker(collectionId, emoji, stars);
          processedCount++;
        } else {
          skippedCount++;
        }

        // Progress update
        if ((i + 1) % 5 === 0) {
          console.log(
            `📊 Progress: ${i + 1}/${data.gifts.length} gifts processed...`
          );
        }

        // Small delay to avoid rate limiting
        await new Promise((resolve) => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`❌ Error processing gift ${i + 1}:`, error.message);
        skippedCount++;
      }
    }

    console.log("\n🎉 Download process completed!");
    console.log(`📊 Results:`);
    console.log(`   ✅ Successfully downloaded: ${processedCount} collections`);
    console.log(`   ❌ Skipped due to errors: ${skippedCount} collections`);
    console.log(`📁 Download markers saved to: ${IMAGES_DIR}`);
    console.log(`📁 TGS files saved to: ${TEMP_DIR}`);
  } catch (error) {
    console.error("❌ Error in main process:", error.message);
  } finally {
    await client.disconnect();
    rl.close();
  }
}

console.log("🚀 Starting sticker download process...");
console.log("📝 This will download actual sticker files from Telegram");
processCollections();
