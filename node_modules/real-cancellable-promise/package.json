{"name": "real-cancellable-promise", "version": "1.2.2", "description": "A simple cancellable promise implementation that cancels the underlying HTTP call.", "keywords": ["promise", "cancelable", "cancellable", "react"], "homepage": "https://github.com/srmagura/real-cancellable-promise", "bugs": "https://github.com/srmagura/real-cancellable-promise/issues", "repository": {"type": "git", "url": "https://github.com/srmagura/real-cancellable-promise.git"}, "license": "MIT", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "yarn clean && rollup -c .config/rollup.config.mjs", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint .", "lint-staged": "lint-staged --no-stash", "prepack": "yarn build", "prettier-all": "prettier . --write", "setup": "husky install", "test": "jest --config .config/jest.config.cjs", "typedoc": "typedoc --options .config/typedoc.json src/"}, "lint-staged": {"*.ts?(x)": ["eslint --max-warnings 0 --fix", "prettier --write"], "*.{md,js,cjs,yml,json}": "prettier --write"}, "devDependencies": {"@rollup/plugin-typescript": "^11.1.0", "@swc/cli": "^0.1.62", "@swc/core": "^1.3.50", "@swc/jest": "^0.2.24", "@types/jest": "^29.5.0", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "eslint": "^8.38.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-promise": "^6.1.1", "husky": "^8.0.3", "jest": "^29.5.0", "lint-staged": "^13.2.1", "prettier": "^2.8.7", "prettier-plugin-packagejson": "^2.4.3", "rimraf": "^5.0.0", "rollup": "^3.20.2", "tslib": "^2.5.0", "typedoc": "^0.24.1", "typescript": "^5.0.4"}, "packageManager": "yarn@3.5.0"}